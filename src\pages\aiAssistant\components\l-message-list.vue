<template>
  <div class="message-list" ref="messageListRef">
    <div v-for="(message, index) in messages" :key="index" class="message-container">
      <!-- 时间戳 -->
      <l-time-stamp v-if="shouldShowTime(message, index)" :time="message.time" />

      <div class="message-row" :class="{ 'user-message': message.isUser }">
        <!-- 任务确认卡片 -->
        <l-task-confirm-card
          v-if="message.type === 'task-confirm'"
          :recognizedContent="message.recognizedContent"
          @confirm="(content) => handleTaskConfirm(message, content)"
          @cancel="() => handleTaskCancel(message)"
        />
        <!-- 普通消息气泡组件 -->
        <l-message-bubble
          v-else
          :content="message.content"
          :type="message.type || 'text'"
          :audio-url="message.audioUrl"
          :audio-duration="message.audioDuration"
          :is-user="message.isUser"
          :streaming="message.status === 'streaming'"
          :is-collapsed="message.isCollapsed"
          :on-toggle="message.onToggle"
          :is-transcribing="message.isTranscribing"
          :transcribe-result="message.transcribeResult"
          :status-message="message.statusMessage"
          :task-summary="message.summary"
          :query-result="message.queryResult"
        />
      </div>
    </div>

    <!-- 全新的加载状态气泡 -->
    <div v-if="aiState.loading.show" class="message-container loading-container">
      <div class="message-row">
        <l-loading-bubble :loading="aiState.loading" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import dayjs from 'dayjs'
import LMessageBubble from './l-message-bubble.vue'
import LTimeStamp from './l-time-stamp.vue'
import LTaskConfirmCard from './l-task-confirm-card.vue'
import LLoadingBubble from './l-loading-bubble.vue'

const props = defineProps({
  messages: {
    type: Array,
    default: () => [],
  },
  // 全新的AI状态管理对象
  aiState: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['task-confirm', 'task-cancel'])

const messageListRef = ref(null)

// 处理任务确认
const handleTaskConfirm = (message, content) => {
  emit('task-confirm', { messageId: message._id, content })
}

// 处理任务取消
const handleTaskCancel = (message) => {
  emit('task-cancel', { messageId: message._id })
}

// 判断是否显示时间戳（每组消息只显示一次）
const shouldShowTime = (message, index) => {
  if (index === 0) return true

  const prevMessage = props.messages[index - 1]
  // 如果时间差超过 5 分钟，显示新的时间戳
  return dayjs(message.time).diff(dayjs(prevMessage.time), 'minute') > 5
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    scrollToBottom()
  },
  { deep: true }
)

// 监听加载状态变化，自动滚动到底部
watch(
  () => props.loadingState,
  () => {
    scrollToBottom()
  },
  { deep: true }
)

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
})

onMounted(() => {
  scrollToBottom()
})
</script>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  padding: 16px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }

  .message-container {
    margin-bottom: 16px;

    .message-row {
      display: flex;
      justify-content: flex-start;

      &.user-message {
        justify-content: flex-end;
      }
    }

    &.loading-container {
      .message-row {
        justify-content: flex-start;
      }
    }
  }

  // 独立加载气泡样式
  .loading-bubble {
    max-width: 70%;
    border-radius: 12px;
    border-top-left-radius: 4px;
    padding: 10px 15px;
    background-color: var(--color-white);
    color: var(--color-gray-800);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-gray-200);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .loading-dots {
      display: flex;
      column-gap: 4px;
      height: 24px;
      align-items: center;

      .dot {
        width: 8px;
        height: 8px;
        background-color: var(--color-gray-400);
        border-radius: 50%;
        animation: loading-pulse 1.4s ease-in-out infinite;
      }
    }

    .loading-text {
      font-size: 14px;
      color: var(--color-gray-600);
      text-align: center;
    }
  }
}

@keyframes loading-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.15);
    opacity: 1;
  }
}
</style>
