/**
 * AI 云对象主模块
 *
 * 提供智能任务执行和流式聊天功能：
 * - 任务意图识别和执行计划生成
 * - 多步骤任务的自动化执行
 * - 实时流式聊天响应
 * - Todo 工具集成和测试功能
 * - 执行上下文管理和数据传递
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const { IntelligentExecutionPlanner } = require('./modules/planner')
const { executeRobustPlan } = require('./modules/executor')
const { globalPerformanceMonitor } = require('./modules/performance')
const { OpenAI } = require('openai')
const {
  DEFAULT_SYSTEM_PROMPT,
  doubaoParams,
  SSE_MESSAGE_TYPES,
  createSSEMessage,
  generateSessionId,
} = require('./modules/config.js')
const { ExecutionContextManager } = require('./modules/context')
const TodoTool = require('./modules/todo')
const { createLogger } = require('./modules/utils')

/**
 * 处理任务类型的流程
 * @param {Object} sseChannel - SSE 通道
 * @param {string} sessionId - 会话 ID
 * @param {string} message - 用户消息
 * @param {Object} logger - 日志记录器
 */
async function handleTaskFlow(sseChannel, sessionId, message, logger) {
  const taskFlowStartTime = Date.now()

  // 详细日志：任务流程开始
  logger.info('=== 任务流程开始 ===', {
    sessionId,
    messageLength: message.length,
    messagePreview: message.substring(0, 200),
    startTime: new Date(taskFlowStartTime).toISOString(),
    timestamp: taskFlowStartTime,
  })

  try {
    // 1. 任务准备阶段
    const preparationStartTime = Date.now()
    logger.step('任务准备阶段开始', {
      phase: 'preparation',
      startTime: new Date(preparationStartTime).toISOString(),
    })

    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TASK_PREPARATION, sessionId, { message: '正在生成执行计划...' })
    )

    const preparationEndTime = Date.now()
    logger.info('任务准备消息推送成功', {
      phase: 'preparation',
      duration: preparationEndTime - preparationStartTime,
      endTime: new Date(preparationEndTime).toISOString(),
    })

    // 2. 生成任务执行计划
    const planGenerationStartTime = Date.now()
    logger.step('生成智能执行计划开始', {
      phase: 'plan_generation',
      message: message.substring(0, 100),
      startTime: new Date(planGenerationStartTime).toISOString(),
    })

    const contextId = IntelligentExecutionPlanner.generateUUID()
    const context = new ExecutionContextManager(contextId)

    logger.debug('执行上下文创建完成', {
      contextId,
      contextType: typeof context,
      hasContext: !!context,
    })

    const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, 'task')

    const planGenerationEndTime = Date.now()
    logger.info('执行计划生成完成', {
      phase: 'plan_generation',
      planId: executionPlan.planId,
      totalSteps: executionPlan.totalSteps,
      estimatedTotalTime: executionPlan.estimatedTotalTime,
      planType: executionPlan.type,
      hasSteps: !!executionPlan.steps,
      stepsArray: executionPlan.steps ? executionPlan.steps.length : 0,
      duration: planGenerationEndTime - planGenerationStartTime,
      endTime: new Date(planGenerationEndTime).toISOString(),
      executionPlanDetails: JSON.stringify(executionPlan, null, 2),
    })

    // 3. 执行任务步骤
    if (executionPlan.totalSteps > 0) {
      const executionStartTime = Date.now()
      logger.step('开始执行任务步骤', {
        phase: 'execution',
        totalSteps: executionPlan.totalSteps,
        startTime: new Date(executionStartTime).toISOString(),
      })

      // 使用真实的执行引擎（添加超时监控）
      try {
        logger.info('调用真实执行引擎', {
          executorFunction: 'executeRobustPlan',
          planId: executionPlan.planId,
          contextId: context.sessionId,
        })

        // 设置执行超时监控（4分钟）
        const executionTimeoutMs = 240000 // 4分钟
        const executionTimeout = setTimeout(() => {
          logger.error('任务执行超时警告', {
            planId: executionPlan.planId,
            timeoutMs: executionTimeoutMs,
            elapsedTime: Date.now() - executionStartTime,
            phase: 'execution_timeout_warning',
          })
        }, executionTimeoutMs)

        let executionResult
        try {
          executionResult = await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)
          clearTimeout(executionTimeout)
        } catch (error) {
          clearTimeout(executionTimeout)
          throw error
        }

        const executionEndTime = Date.now()
        logger.info('真实执行引擎完成', {
          phase: 'execution',
          executionStatus: executionResult.status,
          totalExecutionTime: executionResult.totalExecutionTime,
          duration: executionEndTime - executionStartTime,
          endTime: new Date(executionEndTime).toISOString(),
          executionResultDetails: JSON.stringify(executionResult, null, 2),
        })

        // 检查执行结果状态
        if (executionResult.status === 'completed') {
          logger.info('所有步骤执行成功', {
            completedSteps: executionResult.steps.filter((s) => s.status === 'completed').length,
            failedSteps: executionResult.steps.filter((s) => s.status === 'failed').length,
          })
        } else if (executionResult.status === 'failed') {
          logger.error('执行计划失败', {
            error: executionResult.error,
            failedSteps: executionResult.steps.filter((s) => s.status === 'failed').length,
          })
        }
      } catch (executionError) {
        logger.error('真实执行引擎异常', executionError, {
          errorName: executionError.name,
          errorMessage: executionError.message,
          errorStack: executionError.stack,
          planId: executionPlan.planId,
        })

        // 直接抛出异常，不进行模拟执行回退
        throw executionError
      }

      // 4. 所有任务完成
      const completionTime = Date.now()
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.TASK_ALL_COMPLETE, sessionId, {
          message: '所有任务执行完成',
          summary: `成功执行了${executionPlan.totalSteps}个步骤`,
          totalDuration: completionTime - taskFlowStartTime,
        })
      )

      logger.info('任务执行完成消息推送成功', {
        phase: 'completion',
        totalSteps: executionPlan.totalSteps,
        completionTime: new Date(completionTime).toISOString(),
      })
    } else {
      logger.warn('执行计划为空，无任务需要执行', {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        planDetails: JSON.stringify(executionPlan, null, 2),
      })

      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.TASK_ALL_COMPLETE, sessionId, {
          message: '未检测到需要执行的任务',
          summary: '当前请求不需要执行具体任务',
        })
      )
    }

    const taskFlowEndTime = Date.now()
    logger.info('=== 任务流程完成 ===', {
      sessionId,
      totalDuration: taskFlowEndTime - taskFlowStartTime,
      endTime: new Date(taskFlowEndTime).toISOString(),
      success: true,
    })
  } catch (error) {
    const taskFlowEndTime = Date.now()
    logger.error('=== 任务流程异常 ===', error, {
      sessionId,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      totalDuration: taskFlowEndTime - taskFlowStartTime,
      endTime: new Date(taskFlowEndTime).toISOString(),
      success: false,
    })

    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.ERROR, sessionId, {
        error: `任务执行失败：${error.message}`,
        errorDetails: {
          name: error.name,
          message: error.message,
          timestamp: new Date().toISOString(),
        },
      })
    )
  }
}

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    // 创建日志记录器
    const logger = createLogger('chatStreamSSE')

    // 生成会话 ID
    const sessionId = generateSessionId()

    // 记录函数入口
    logger.enter('chatStreamSSE', {
      sessionId,
      hasChannel: !!channel,
      messageLength: message ? message.length : 0,
      historyCount: history_records ? history_records.length : 0,
    })

    // 参数验证阶段
    logger.step('参数验证开始', { message: message ? '有消息' : '无消息', channel: channel ? '有通道' : '无通道' })

    if (!message) {
      logger.warn('参数验证失败：消息内容为空')
      const result = {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
      logger.exit('chatStreamSSE', result)
      return result
    }

    if (!channel) {
      logger.warn('参数验证失败：SSE Channel 为空')
      const result = {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
      logger.exit('chatStreamSSE', result)
      return result
    }

    logger.step('参数验证通过', { messagePreview: message.substring(0, 100) })

    try {
      // 反序列化 SSE Channel
      logger.step('反序列化 SSE Channel', { channelType: typeof channel })
      const sseChannel = uniCloud.deserializeSSEChannel(channel)
      logger.info('SSE Channel 反序列化成功')

      // 1. 推送开始处理消息
      logger.step('推送开始处理消息')
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.PROCESSING_START, sessionId, { message: '开始处理您的请求...' })
      )
      logger.info('开始处理消息推送成功')

      // 2. 推送意图分析消息
      logger.step('推送意图分析消息')
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.INTENT_ANALYZING, sessionId, { message: '正在分析您的意图...' })
      )
      logger.info('意图分析消息推送成功')

      // 初始化豆包 AI 客户端
      logger.step('初始化豆包 AI 客户端', {
        hasApiKey: !!doubaoParams.apiKey,
        baseURL: doubaoParams.baseURL,
      })
      const openai = new OpenAI(doubaoParams)
      logger.info('豆包 AI 客户端初始化成功')

      // 构建对话消息数组
      logger.step('构建对话消息数组', {
        systemPromptLength: DEFAULT_SYSTEM_PROMPT.length,
        historyCount: history_records.length,
      })

      const messages = [
        {
          role: 'system',
          content: DEFAULT_SYSTEM_PROMPT,
        },
        ...history_records,
      ]
      if (message)
        messages.push({
          role: 'user',
          content: message,
        })

      logger.info('对话消息数组构建完成', { totalMessages: messages.length })

      // 创建流式 AI 响应，启用实时数据传输
      logger.step('创建流式 AI 响应', {
        model: 'doubao-seed-1-6-250615',
        stream: true,
        timeout: 300000,
      })

      const streamResponse = await openai.chat.completions.create({
        messages, // 对话上下文
        model: 'doubao-seed-1-6-250615', // 使用的 AI 模型
        stream: true, // 启用流式响应，关键设置
        timeout: 300000, // 5 分钟超时（毫秒），防止长时间等待
      })

      logger.info('流式 AI 响应创建成功')

      // 初始化流式处理相关变量
      logger.step('初始化流式处理变量')
      let fullContent = '' // 累积的完整 AI 响应内容，用于最终的意图解析
      let chunkCount = 0 // 推送的数据块计数，用于统计和调试
      let intentType = null // 识别的意图类型：task|chat
      let isChatReplyStarted = false // 是否开始推送闲聊回复的标志位
      let chatReply = '' // 提取的闲聊回复内容，仅用于 chat 类型

      // 正则表达式：匹配 AI 返回的意图类型和闲聊回复
      // task 类型只有意图类型，chat 类型有意图类型和闲聊回复
      const intentTypeRegex = /「意图类型」：(task|chat)/
      const chatReplyRegex = /「闲聊回复」：([\s\S]*)/

      logger.info('流式处理变量初始化完成', {
        intentTypeRegex: intentTypeRegex.toString(),
        chatReplyRegex: chatReplyRegex.toString(),
      })

      // 流式处理 AI 响应数据
      logger.step('开始流式处理 AI 响应数据')

      for await (const chunk of streamResponse) {
        // 提取当前数据块的内容，处理可能的空值情况
        const content = chunk.choices[0]?.delta?.content || ''

        if (content) {
          fullContent += content // 累积完整内容，用于后续的正则匹配
          chunkCount++ // 增加数据块计数，用于统计和调试

          // 每 100 个 chunk 记录一次进度
          if (chunkCount % 100 === 0) {
            logger.debug('流式处理进度', {
              chunkCount,
              contentLength: fullContent.length,
              hasIntentType: !!intentType,
            })
          }

          // 第一阶段：检测意图类型
          // 在累积的内容中查找意图类型标识，一旦找到就立即处理
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1] // 提取意图类型：task|chat

              logger.info('检测到意图类型', {
                intentType,
                chunkCount,
                contentLength: fullContent.length,
              })

              // 3. 推送意图识别结果
              await sseChannel.write(
                createSSEMessage(SSE_MESSAGE_TYPES.INTENT_RECOGNIZED, sessionId, {
                  intentType: intentType,
                  confidence: 0.95,
                  description: intentType === 'chat' ? '检测到闲聊意图' : '检测到任务执行意图',
                })
              )

              logger.info('意图识别结果推送成功', { intentType })
              continue // 跳过当前块的推送，避免重复发送相同信息
            }
          }

          // 第二阶段：根据意图类型处理内容推送
          if (intentType === 'chat') {
            // chat 类型：检测闲聊回复开始标识
            if (!isChatReplyStarted) {
              const replyMatch = chatReplyRegex.exec(fullContent)
              if (replyMatch) {
                isChatReplyStarted = true
                chatReply = replyMatch[1] // 提取已有的闲聊回复内容

                logger.info('检测到闲聊回复开始', {
                  chatReplyLength: chatReply.length,
                  chunkCount,
                })

                // 推送聊天回复开始消息
                await sseChannel.write(
                  createSSEMessage(SSE_MESSAGE_TYPES.CHAT_RESPONSE_START, sessionId, { message: '正在生成回复...' })
                )

                // 推送已有的闲聊回复内容
                await sseChannel.write(
                  createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
                    content: chatReply,
                    isComplete: false,
                    totalLength: null,
                  })
                )

                logger.debug('闲聊回复开始内容推送成功')
                continue // 跳过当前块的推送，避免重复发送
              }
            } else {
              // 持续推送闲聊回复内容块
              await sseChannel.write(
                createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
                  content: content,
                  isComplete: false,
                  totalLength: null,
                })
              )
              chatReply += content // 累积闲聊回复内容

              // 每 50 个字符记录一次聊天内容进度
              if (chatReply.length % 50 === 0) {
                logger.debug('闲聊回复内容累积', {
                  chatReplyLength: chatReply.length,
                  chunkCount,
                })
              }
            }
          } else if (intentType === 'task') {
            // task 类型：不需要推送额外内容，直接进入任务执行阶段
            logger.debug('任务类型检测到，等待流式处理完成')
          } else {
            // 尚未检测到意图类型，继续累积内容
            logger.debug('尚未检测到意图类型，继续累积内容', {
              contentLength: fullContent.length,
              chunkCount,
            })
          }
        }
      }

      logger.info('流式处理完成', {
        totalChunks: chunkCount,
        fullContentLength: fullContent.length,
        intentType,
        isChatReplyStarted,
        chatReplyLength: chatReply.length,
      })

      // 根据意图类型分支处理
      if (intentType === 'chat') {
        // 聊天类型：推送聊天回复完成消息
        await sseChannel.write(
          createSSEMessage(SSE_MESSAGE_TYPES.CHAT_RESPONSE_END, sessionId, { message: '回复完成' })
        )
        logger.info('聊天回复完成消息推送成功')
      } else if (intentType === 'task') {
        // 任务类型：执行任务流程
        await handleTaskFlow(sseChannel, sessionId, message, logger)
      }

      // 推送会话结束消息
      await sseChannel.end(createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId))
      logger.info('会话结束消息推送成功')

      // 返回处理完成的结果
      const result = {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete',
          content: intentType === 'chat' ? chatReply : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
          sessionId: sessionId,
        },
      }

      logger.exit('chatStreamSSE', result)
      return result
    } catch (error) {
      // 错误处理：捕获并处理执行过程中的所有异常
      logger.exitError('chatStreamSSE', error)

      // 记录详细的错误信息
      logger.error('chatStreamSSE 执行异常', error, {
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack,
        hasChannel: !!channel,
        messageLength: message ? message.length : 0,
      })

      // 尝试通过 SSE Channel 发送错误消息给前端
      try {
        if (channel) {
          logger.step('尝试通过 SSE Channel 发送错误消息')
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end(
            createSSEMessage(
              SSE_MESSAGE_TYPES.ERROR,
              generateSessionId(), // 生成临时会话 ID
              { error: error.message || '调用 AI 流式接口失败' }
            )
          )
          logger.info('错误消息通过 SSE Channel 发送成功')
        }
      } catch (channelError) {
        // 如果连错误消息都发送失败，记录但忽略该错误
        logger.error('SSE Channel 错误消息发送失败', channelError, {
          originalError: error.message,
          channelErrorMessage: channelError.message,
        })
      }

      // 返回错误结果给调用方
      const errorResult = {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }

      logger.exit('chatStreamSSE', errorResult)
      return errorResult
    }
  },

  /**
   * 测试 Todo 工具功能
   * 提供一个简单的测试接口，用于验证 Todo 工具的基本功能
   * @returns {object} 测试结果
   */
  async testTodoTool() {
    const logger = createLogger('testTodoTool')
    logger.enter('testTodoTool')

    try {
      // 创建 Todo 工具实例
      const todoTool = new TodoTool()

      // 测试获取任务列表
      logger.step('测试获取任务列表')
      const tasksResult = await todoTool.execute('getTasks', {})
      logger.info('获取任务列表结果', { result: tasksResult })

      // 测试创建任务
      logger.step('测试创建任务')
      const createResult = await todoTool.execute('createTask', {
        title: '测试任务',
        content: '这是一个测试任务',
        priority: 'medium',
      })
      logger.info('创建任务结果', { result: createResult })

      const result = {
        errCode: 0,
        errMsg: 'success',
        data: {
          tasksResult,
          createResult,
          timestamp: new Date().toISOString(),
        },
      }

      logger.exit('testTodoTool', result)
      return result
    } catch (error) {
      logger.exitError('testTodoTool', error)
      return {
        errCode: 'TEST_ERROR',
        errMsg: error.message || '测试失败',
      }
    }
  },
}
