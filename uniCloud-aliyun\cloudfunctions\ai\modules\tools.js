// 工具调用模块
// 负责实际调用内置工具

const { TOOL_REGISTRY } = require('./config')

// 内置工具实例缓存
const builtinTools = new Map()

/**
 * 获取内置工具实例
 * 使用单例模式缓存工具实例，提高性能
 * @param {string} toolName - 工具名称
 * @returns {object} 工具实例
 */
function getBuiltinTool(toolName) {
  if (!builtinTools.has(toolName)) {
    try {
      // 根据工具名称动态加载对应的内置工具类
      let ToolClass
      switch (toolName) {
        case 'todo':
          ToolClass = require('./todo')
          break
        case 'ai':
          // AI 工具类，用于处理查询结果等 AI 相关任务
          ToolClass = class AITool {
            async execute(method, parameters) {
              switch (method) {
                case 'processQueryResult':
                  return this.processQueryResult(parameters)
                default:
                  throw new Error(`AI 工具不支持的方法：${method}`)
              }
            }

            async processQueryResult(parameters) {
              const { queryType, queryData, userQuery, context } = parameters

              console.log(`[AITool] [processQueryResult] 开始处理查询结果`, {
                queryType,
                hasQueryData: !!queryData,
                userQuery,
                hasContext: !!context,
                timestamp: new Date().toISOString(),
              })

              // 这里实际上不需要真正的 AI 处理，因为在 executor 中已经处理了
              // 这个工具主要是为了满足 AI 规划器的要求，直接返回成功状态
              const result = {
                errCode: null,
                errMsg: '查询结果处理成功',
                data: {
                  formattedResponse: '查询结果已处理',
                  summary: '处理完成',
                  queryType,
                  processedAt: new Date().toISOString(),
                },
              }

              console.log(`[AITool] [processQueryResult] 查询结果处理完成`, {
                queryType,
                result: JSON.stringify(result, null, 2),
                timestamp: new Date().toISOString(),
              })

              return result
            }
          }
          break
        default:
          throw new Error(`未知的内置工具：${toolName}`)
      }

      // 创建工具实例并缓存（单例模式）
      const toolInstance = new ToolClass()
      builtinTools.set(toolName, toolInstance)
    } catch (error) {
      throw new Error(`加载内置工具失败：${error.message}`)
    }
  }

  return builtinTools.get(toolName)
}

/**
 * 内置工具调用函数
 * 统一的工具调用入口，支持内置工具的直接调用
 * @param {string} toolName - 工具名称
 * @param {object} parameters - 调用参数
 * @returns {Promise<object>} 工具执行结果
 */
async function callRealTool(toolName, parameters) {
  const callStartTime = Date.now()

  console.log(`[callRealTool] === 工具调用开始 ===`, {
    toolName,
    parameters: JSON.stringify(parameters, null, 2),
    startTime: new Date(callStartTime).toISOString(),
    timestamp: callStartTime,
  })

  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    const error = new Error(`未找到工具：${toolName}`)
    console.error(`[callRealTool] 工具配置查找失败`, {
      toolName,
      availableTools: Object.keys(TOOL_REGISTRY),
      error: error.message,
    })
    throw error
  }

  console.log(`[callRealTool] 工具配置查找成功`, {
    toolName,
    toolConfig: JSON.stringify(toolConfig, null, 2),
    configType: toolConfig.type,
    configTool: toolConfig.tool,
    configMethod: toolConfig.method,
  })

  // 验证工具配置完整性
  if (toolConfig.type !== 'builtin') {
    const error = new Error(`工具 ${toolName} 不是内置工具类型，当前仅支持内置工具调用`)
    console.error(`[callRealTool] 工具类型验证失败`, {
      toolName,
      expectedType: 'builtin',
      actualType: toolConfig.type,
      error: error.message,
    })
    throw error
  }

  if (!toolConfig.tool || !toolConfig.method) {
    const error = new Error(`工具 ${toolName} 配置不完整，缺少 tool 或 method 字段`)
    console.error(`[callRealTool] 工具配置完整性验证失败`, {
      toolName,
      hasTool: !!toolConfig.tool,
      hasMethod: !!toolConfig.method,
      toolConfig: JSON.stringify(toolConfig, null, 2),
      error: error.message,
    })
    throw error
  }

  try {
    console.log(`[callRealTool] 开始获取工具实例`, {
      toolName,
      toolType: toolConfig.tool,
      method: toolConfig.method,
    })

    const toolInstance = getBuiltinTool(toolConfig.tool)

    console.log(`[callRealTool] 工具实例获取成功`, {
      toolName,
      toolType: toolConfig.tool,
      instanceType: typeof toolInstance,
      hasExecuteMethod: typeof toolInstance.execute === 'function',
    })

    console.log(`[callRealTool] === 开始执行工具方法 ===`, {
      toolName,
      toolType: toolConfig.tool,
      method: toolConfig.method,
      parameters: JSON.stringify(parameters, null, 2),
      executeStartTime: new Date().toISOString(),
    })

    const result = await toolInstance.execute(toolConfig.method, parameters)
    const callEndTime = Date.now()

    console.log(`[callRealTool] === 工具方法执行完成 ===`, {
      toolName,
      toolType: toolConfig.tool,
      method: toolConfig.method,
      executionDuration: callEndTime - callStartTime,
      endTime: new Date(callEndTime).toISOString(),
      resultStatus:
        result?.errCode !== undefined
          ? result.errCode === null || result.errCode === 0
            ? 'success'
            : 'error'
          : 'unknown',
      resultErrCode: result?.errCode,
      resultErrMsg: result?.errMsg,
      resultData: result?.data ? JSON.stringify(result.data, null, 2) : 'no data',
      fullResult: JSON.stringify(result, null, 2),
    })

    // 特别关注任务创建相关的调用
    if (toolName === 'createTask' || toolConfig.method === 'createTask') {
      console.log(`[callRealTool] !!! 任务创建工具执行结果 !!!`, {
        toolName,
        method: toolConfig.method,
        inputParameters: JSON.stringify(parameters, null, 2),
        outputResult: JSON.stringify(result, null, 2),
        isSuccess: result?.errCode === null || result?.errCode === 0,
        errorCode: result?.errCode,
        errorMessage: result?.errMsg,
        createdTaskId: result?.data?.id,
        createdTaskTitle: result?.data?.title,
        executionDuration: callEndTime - callStartTime,
        timestamp: new Date().toISOString(),
      })
    }

    console.log(`[callRealTool] === 工具调用成功完成 ===`, {
      toolName,
      totalDuration: callEndTime - callStartTime,
      success: true,
    })

    return result
  } catch (error) {
    const callEndTime = Date.now()

    console.error(`[callRealTool] === 工具调用异常 ===`, error, {
      toolName,
      toolType: toolConfig.tool,
      method: toolConfig.method,
      parameters: JSON.stringify(parameters, null, 2),
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      executionDuration: callEndTime - callStartTime,
      endTime: new Date(callEndTime).toISOString(),
      success: false,
    })

    // 特别关注任务创建相关的错误
    if (toolName === 'createTask' || toolConfig.method === 'createTask') {
      console.error(`[callRealTool] !!! 任务创建工具执行失败 !!!`, error, {
        toolName,
        method: toolConfig.method,
        inputParameters: JSON.stringify(parameters, null, 2),
        errorDetails: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        executionDuration: callEndTime - callStartTime,
        timestamp: new Date().toISOString(),
      })
    }

    throw new Error(`内置工具执行失败：${error.message}`)
  }
}

// 获取上下文更新信息
function getContextUpdates(context) {
  const updates = {}
  for (const [key, value] of context.contextData.entries()) {
    updates[key] = value
  }
  return updates
}

// 生成执行摘要
function generateExecutionSummary(executionPlan, context) {
  const completedSteps = executionPlan.steps.filter((s) => s.status === 'completed')
  const failedSteps = executionPlan.steps.filter((s) => s.status === 'failed')

  return {
    totalSteps: executionPlan.totalSteps,
    completedSteps: completedSteps.length,
    failedSteps: failedSteps.length,
    totalExecutionTime: executionPlan.totalExecutionTime,
    averageStepTime:
      completedSteps.length > 0
        ? Math.round(completedSteps.reduce((sum, s) => sum + s.executionTime, 0) / completedSteps.length)
        : 0,
    contextDataKeys: Array.from(context.contextData.keys()),
    success: failedSteps.length === 0,
  }
}

// 辅助函数：计算动态引用数量
function countDynamicReferences(parameters) {
  let count = 0
  const paramStr = JSON.stringify(parameters)

  // 计算各种动态引用
  count += (paramStr.match(/\$context\./g) || []).length
  count += (paramStr.match(/\$step\./g) || []).length
  count += (paramStr.match(/\$filter\(/g) || []).length

  return count
}

module.exports = {
  callRealTool,
  getContextUpdates,
  generateExecutionSummary,
  countDynamicReferences,
}
