﻿/**
 * 执行计划生成模块
 *
 * 主要功能：
 * 1. 根据用户输入和意图类型生成智能执行计划
 * 2. 使用 AI 分析用户需求，生成最优的工具调用序列
 * 3. 支持动态参数解析和上下文关联
 * 4. 提供执行时间估算和性能优化
 *
 * 核心特性：
 * - AI 驱动的计划生成：使用豆包 AI 分析用户意图，生成智能计划
 * - 工具自动选择：从工具注册表中智能选择最合适的工具组合
 * - 参数智能推导：支持静态参数和动态参数的自动解析
 * - 执行优化：预估执行时间，优化步骤顺序和资源使用
 *
 * 技术架构：
 * - 基于 OpenAI SDK 与豆包 AI 进行交互
 * - 采用 JSON 格式的结构化计划描述
 * - 集成工具注册表进行工具配置管理
 * - 支持计划的序列化和持久化存储
 *
 * 设计模式：
 * - 策略模式：不同意图类型对应不同的生成策略
 * - 工厂模式：动态创建执行步骤和参数配置
 * - 建造者模式：逐步构建复杂的执行计划对象
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const OpenAI = require('openai')
const { doubaoParams, TOOL_REGISTRY } = require('./config')
const { createLogger } = require('./utils')

/**
 * 智能执行计划生成器
 * 使用 AI 生成更复杂和智能的执行计划
 *
 * 核心能力：
 * - 自然语言理解：深度分析用户输入的语义和意图
 * - 智能工具选择：根据任务需求自动选择最优工具组合
 * - 参数智能推导：自动推导工具调用所需的参数
 * - 执行优化：优化步骤顺序，提高执行效率
 *
 * 生成流程：
 * 1. 构建 AI 分析提示词
 * 2. 调用豆包 AI 进行意图分析和计划生成
 * 3. 解析 AI 返回的 JSON 格式计划
 * 4. 构建标准化的执行步骤对象
 * 5. 计算执行时间和依赖关系
 */
class IntelligentExecutionPlanner {
  /**
   * 生成执行计划
   * 这是计划生成器的核心方法，使用 AI 分析用户意图并生成智能执行计划
   *
   * @param {string} userInput - 用户输入内容，用于 AI 分析和参数推导
   * @param {string} intentType - 意图类型：task
   * @returns {Object} 完整的执行计划对象
   *
   * 执行计划对象结构：
   * - planId: 计划唯一标识（UUID）
   * - userInput: 用户原始输入
   * - intentType: 识别的意图类型
   * - steps: 执行步骤数组
   * - totalSteps: 总步骤数
   * - status: 计划状态（pending/running/completed/failed）
   * - startTime: 计划创建时间戳
   * - estimatedTotalTime: 预估总执行时间（毫秒）
   *
   * 生成流程：
   * 1. 初始化执行计划基础结构
   * 2. 构建 AI 分析提示词
   * 3. 调用豆包 AI 进行智能分析
   * 4. 解析 AI 返回的 JSON 计划数据
   * 5. 构建标准化的执行步骤对象
   * 6. 计算总执行时间和步骤数量
   */
  static async generatePlan(userInput, intentType) {
    const logger = createLogger('planner')

    logger.enter('generatePlan', {
      userInputLength: userInput ? userInput.length : 0,
      intentType,
      userInputPreview: userInput ? userInput.substring(0, 100) : '',
    })

    // 初始化执行计划的基础结构
    logger.step('初始化执行计划基础结构')
    const executionPlan = {
      planId: this.generateUUID(), // 生成唯一的计划标识
      userInput: userInput, // 保存用户原始输入，用于后续分析
      intentType: intentType, // 保存意图类型，用于计划分类
      steps: [], // 执行步骤数组，将由 AI 生成填充
      entities: {}, // AI 从用户输入中提取的结构化实体数据
      totalSteps: 0, // 总步骤数，在步骤构建完成后计算
      status: 'pending', // 计划状态，初始为待执行
      startTime: Date.now(), // 计划创建时间戳
      estimatedTotalTime: 0, // 预估总执行时间，在步骤构建完成后计算
    }

    // 构建 AI 分析提示词，指导 AI 理解用户意图并生成计划
    logger.step('构建 AI 分析提示词')
    const analysisPrompt = this.buildAnalysisPrompt(userInput, intentType)
    logger.debug('AI 分析提示词构建完成', {
      promptLength: analysisPrompt.length,
      promptPreview: analysisPrompt.substring(0, 200),
    })

    try {
      // 调用豆包 AI 进行智能分析，获取结构化的计划数据
      logger.step('调用豆包 AI 进行智能分析')
      const aiResponse = await this.callAI(analysisPrompt)
      logger.info('AI 响应获取成功', { responseLength: aiResponse.length })

      logger.step('解析 AI 返回的 JSON 计划数据')
      const planData = JSON.parse(aiResponse) // 解析 AI 返回的 JSON 格式计划
      logger.info('AI 计划数据解析成功', {
        hasSteps: !!planData.steps,
        stepsCount: planData.steps ? planData.steps.length : 0,
        hasEntities: !!planData.entities,
        entitiesCount: planData.entities ? Object.keys(planData.entities).length : 0,
      })

      // 保存 AI 提取的实体
      if (planData.entities) {
        executionPlan.entities = planData.entities
        logger.info('AI 提取的实体保存完成', {
          entityKeys: Object.keys(planData.entities),
        })
      }

      // 保存任务类型
      if (planData.taskType) {
        executionPlan.taskType = planData.taskType
        logger.info('任务类型保存完成', {
          taskType: planData.taskType,
        })
      }

      // 构建执行步骤对象
      logger.step('构建执行步骤对象')
      if (planData.steps && Array.isArray(planData.steps)) {
        for (let i = 0; i < planData.steps.length; i++) {
          const stepData = planData.steps[i]
          const step = {
            stepId: `step_${i + 1}_${Date.now()}`, // 生成唯一的步骤标识
            toolName: stepData.toolName, // 工具名称，必须在工具注册表中存在
            description: stepData.description || `执行 ${stepData.toolName}`, // 步骤描述
            parameters: stepData.parameters || {}, // 工具调用参数
            dependencies: stepData.dependencies || [], // 步骤依赖关系
            reasoning: stepData.reasoning || '', // AI 的推理过程
            status: 'pending', // 步骤状态，初始为待执行
            retryCount: 0, // 重试次数，初始为 0
            maxRetries: 3, // 最大重试次数
            estimatedTime: this.estimateStepTime(stepData.toolName), // 预估执行时间
            executionTime: 0, // 实际执行时间，执行完成后填充
          }
          executionPlan.steps.push(step)
        }
      }

      // 计算总步骤数和预估总时间
      executionPlan.totalSteps = executionPlan.steps.length
      executionPlan.estimatedTotalTime = executionPlan.steps.reduce((total, step) => total + step.estimatedTime, 0)

      logger.info('执行计划生成完成', {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime,
        taskType: executionPlan.taskType,
      })

      logger.exit('generatePlan', executionPlan)
      return executionPlan
    } catch (error) {
      logger.exitError('generatePlan', error)
      // AI 分析失败时，生成默认计划
      logger.warn('AI 计划生成失败，使用默认计划', { error: error.message })
      return this.generateDefaultPlan(userInput, intentType)
    }
  }

  static buildAnalysisPrompt(userInput, intentType) {
    const toolPrompt = this.generateToolPrompt(TOOL_REGISTRY)
    return `分析用户输入："${userInput}"
      意图类型：${intentType}
      ${toolPrompt}
      请分析用户意图，提取关键实体，并生成执行计划。
      ## 任务类型识别：
      **查询类任务**：用户想要获取、查看、搜索信息（如"查找任务"、"显示项目"、"有哪些任务"）
      **操作类任务**：用户想要创建、修改、删除数据（如"创建任务"、"完成任务"、"删除项目"）
      ## 查询类任务特殊要求：
      1. 必须在最后添加一个 "processQueryResult" 步骤来处理和格式化查询结果
      2. 该步骤应该引用查询工具的返回数据，并生成用户友好的回复
      3. 示例：如果用户问"有哪些未完成的任务"，最后应该有一个步骤来整理任务列表并生成回复
      返回 JSON 格式：
      {
        "analysis": "对用户意图的详细分析，说明你如何理解这个任务，并明确标识这是查询类还是操作类任务。",
        "taskType": "query|operation - 标识任务类型",
        "entities": {
          "projectName": "提取到的项目名称，如果没有则为 null",
          "taskTitle": "提取到的任务标题，如果没有则为 null",
          "dueDate": "提取到的日期，ISO 8601 格式，如果没有则为 null",
          "priority": "提取到的优先级数字，如果没有则为 null",
          "content": "提取到的任务内容，如果没有则为 null"
        },
        "steps": [
          {
            "toolName": "工具名称（必须是上述工具列表中的一个）",
            "description": "步骤描述，说明该步骤的作用和目的",
            "parameters": {
              "param1": "静态值或动态引用",
              "param2": "$plan.entities.taskTitle",
              "param3": "$step.previousStepId.data.id"
            },
            "dependencies": ["前置步骤的 stepId"],
            "reasoning": "解释为什么选择这个工具、如何设置参数，以及如何使用返回值。"
          }
        ]
      }`
  }

  static generateToolPrompt(toolRegistry) {
    let toolPrompt = '可用工具列表：\n\n'
    for (const [toolName, toolConfig] of Object.entries(toolRegistry)) {
      toolPrompt += `**${toolName}**：${toolConfig.description}\n`
      if (toolConfig.parameters && toolConfig.parameters.properties) {
        toolPrompt += '参数：\n'
        for (const [paramName, paramConfig] of Object.entries(toolConfig.parameters.properties)) {
          const required =
            toolConfig.parameters.required && toolConfig.parameters.required.includes(paramName)
              ? '（必填）'
              : '（可选）'
          toolPrompt += `- ${paramName}${required}：${paramConfig.description}\n`
        }
      }
      toolPrompt += '\n'
    }
    return toolPrompt
  }

  static async callAI(prompt) {
    const openai = new OpenAI(doubaoParams)
    const response = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的任务执行计划生成器。' },
        { role: 'user', content: prompt },
      ],
      model: 'doubao-seed-1-6-250615',
      stream: false,
      timeout: 120000, // 增加到2分钟超时
    })
    return response.choices[0].message.content
  }

  static generateDefaultPlan(userInput, intentType) {
    const logger = createLogger('planner')
    logger.info('生成默认执行计划', { userInput: userInput.substring(0, 100), intentType })
    return {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      taskType: 'operation',
      steps: [
        {
          stepId: `step_1_${Date.now()}`,
          toolName: 'createTask',
          description: '创建任务',
          parameters: { title: userInput.replace(/^添加任务[：:]\s*/, '') },
          dependencies: [],
          reasoning: '默认计划：创建任务',
          status: 'pending',
          retryCount: 0,
          maxRetries: 3,
          estimatedTime: 3000,
          executionTime: 0,
        },
      ],
      entities: {},
      totalSteps: 1,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 3000,
    }
  }

  static estimateStepTime(toolName) {
    const timeEstimates = {
      getTasks: 2000,
      getProjects: 1500,
      createTask: 3000,
      createProject: 2500,
      updateTask: 2000,
      updateProject: 2000,
      deleteTask: 1500,
      deleteProject: 1500,
      processQueryResult: 1000,
    }
    return timeEstimates[toolName] || 2000
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0
      const v = c === 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
}

module.exports = { IntelligentExecutionPlanner }
