/**
 * 执行引擎模块
 *
 * 主要功能：
 * 1. 负责执行计划的实际执行，包括简单执行、智能执行和增强执行
 * 2. 提供三个版本的执行引擎，支持不同复杂度的任务执行需求
 * 3. 集成参数验证、动态解析、错误处理和性能监控
 * 4. 提供实时的执行状态推送和进度跟踪
 *
 * 执行引擎版本：
 * - V1.1 基础工具调用引擎：简单的顺序执行，适用于基础功能测试
 * - V1.2 智能执行引擎：支持动态参数解析和智能错误处理
 * - V1.3 增强执行引擎：集成性能监控和高级错误恢复机制
 *
 * 核心特性：
 * - 多版本支持：根据任务复杂度选择合适的执行引擎
 * - 动态参数解析：支持步骤间的数据传递和上下文引用
 * - 智能错误处理：多层次的错误处理和自动恢复机制
 * - 性能监控：实时监控执行性能和资源使用情况
 * - 实时推送：通过 SSE 推送详细的执行状态和结果
 *
 * 技术架构：
 * - 模块化设计：每个执行引擎独立实现，便于维护和扩展
 * - 依赖注入：通过构造函数注入各种服务和工具
 * - 异步执行：全面支持异步操作和并发控制
 * - 状态管理：完整的执行状态跟踪和管理
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const { ParameterValidator } = require('./validator')
const { DynamicParameterResolver } = require('./resolver')
const { EnhancedErrorHandler } = require('./error-handler')
const { callRealTool, getContextUpdates, generateExecutionSummary, countDynamicReferences } = require('./tools')
const { globalPerformanceMonitor } = require('./performance')
const { createLogger } = require('./utils')

/**
 * V1.3 增强的执行引擎
 * 集成错误处理和性能监控的完整执行引擎
 * 支持动态参数解析、智能重试和性能统计
 *
 * @param {Object} executionPlan - 执行计划对象
 * @param {Object} context - 执行上下文管理器
 * @param {Object} sseChannel - SSE 推送通道
 * @param {Object} performanceMonitor - 性能监控器实例
 * @returns {Object} 执行结果
 */
async function executeRobustPlan(executionPlan, context, sseChannel, performanceMonitor = globalPerformanceMonitor) {
  const logger = createLogger('executor')
  const planStartTime = Date.now() // 记录计划开始时间

  logger.enter('executeRobustPlan', {
    planId: executionPlan.planId,
    totalSteps: executionPlan.totalSteps,
    estimatedTotalTime: executionPlan.estimatedTotalTime,
    contextId: context.sessionId,
  })

  try {
    // 推送执行计划信息到前端
    logger.step('推送执行计划信息到前端', {
      planId: executionPlan.planId,
      totalSteps: executionPlan.totalSteps,
    })

    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId, // 计划唯一标识
        totalSteps: executionPlan.totalSteps, // 总步骤数
        estimatedTotalTime: executionPlan.estimatedTotalTime, // 预估总耗时
      },
      timestamp: Date.now(),
    })

    logger.info('执行计划信息推送成功')

    executionPlan.status = 'executing'
    logger.step('开始执行计划步骤', { status: executionPlan.status })

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      logger.step(`执行步骤 ${i + 1}/${executionPlan.steps.length}`, {
        stepId: step.stepId,
        toolName: step.toolName,
        description: step.description,
        estimatedTime: step.estimatedTime,
      })

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime,
        },
        timestamp: Date.now(),
      })

      logger.info('步骤信息推送成功', { stepId: step.stepId })

      step.status = 'executing'
      const stepStartTime = Date.now()
      let stepResult = null
      let stepSuccess = false

      logger.debug('步骤执行开始', {
        stepId: step.stepId,
        startTime: stepStartTime,
        retryCount: step.retryCount || 0,
        maxRetries: step.maxRetries || 3,
      })

      // 带重试机制的步骤执行
      while (step.retryCount <= step.maxRetries) {
        const attemptStartTime = Date.now()
        logger.debug(`步骤执行尝试 ${step.retryCount + 1}/${step.maxRetries + 1}`, {
          stepId: step.stepId,
          toolName: step.toolName,
          attemptNumber: step.retryCount + 1,
          maxAttempts: step.maxRetries + 1,
          attemptStartTime: new Date(attemptStartTime).toISOString(),
        })

        try {
          // 参数解析性能监控
          const paramStartTime = Date.now()
          logger.debug('开始参数解析', {
            stepId: step.stepId,
            toolName: step.toolName,
            originalParams: JSON.stringify(step.parameters, null, 2),
            hasEntities: !!executionPlan.entities,
            entitiesCount: executionPlan.entities ? Object.keys(executionPlan.entities).length : 0,
          })

          // 将主计划的实体传递给步骤进行解析
          step.entities = executionPlan.entities
          const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)
          const paramEndTime = Date.now()

          logger.info('参数解析完成', {
            stepId: step.stepId,
            toolName: step.toolName,
            resolvedParams: JSON.stringify(resolvedParams, null, 2),
            paramResolutionDuration: paramEndTime - paramStartTime,
            paramEndTime: new Date(paramEndTime).toISOString(),
          })

          // 计算动态引用数量
          const dynamicRefCount = countDynamicReferences(step.parameters)
          performanceMonitor.recordParameterResolution(paramStartTime, paramEndTime, dynamicRefCount, true)

          // 参数验证
          logger.debug('开始参数验证', {
            stepId: step.stepId,
            toolName: step.toolName,
            paramsToValidate: JSON.stringify(resolvedParams, null, 2),
          })

          const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)

          logger.info('参数验证完成', {
            stepId: step.stepId,
            toolName: step.toolName,
            validatedParams: JSON.stringify(validatedParams, null, 2),
            validationPassed: true,
          })

          // 真实工具调用
          const toolCallStartTime = Date.now()
          logger.info('=== 开始真实工具调用 ===', {
            stepId: step.stepId,
            toolName: step.toolName,
            method: step.method || 'unknown',
            finalParams: JSON.stringify(validatedParams, null, 2),
            callStartTime: new Date(toolCallStartTime).toISOString(),
            attemptNumber: step.retryCount + 1,
          })

          stepResult = await callRealTool(step.toolName, validatedParams)
          const toolCallEndTime = Date.now()

          logger.info('=== 真实工具调用完成 ===', {
            stepId: step.stepId,
            toolName: step.toolName,
            method: step.method || 'unknown',
            callDuration: toolCallEndTime - toolCallStartTime,
            callEndTime: new Date(toolCallEndTime).toISOString(),
            resultStatus:
              stepResult?.errCode !== undefined
                ? stepResult.errCode === null || stepResult.errCode === 0
                  ? 'success'
                  : 'error'
                : 'unknown',
            resultErrCode: stepResult?.errCode,
            resultErrMsg: stepResult?.errMsg,
            resultData: stepResult?.data ? JSON.stringify(stepResult.data, null, 2) : 'no data',
            fullResult: JSON.stringify(stepResult, null, 2),
            attemptNumber: step.retryCount + 1,
          })

          // 特别关注任务创建相关的调用
          if (step.toolName === 'createTask' || (step.method && step.method.includes('createTask'))) {
            logger.info('!!! 任务创建工具调用详情 !!!', {
              stepId: step.stepId,
              toolName: step.toolName,
              method: step.method,
              inputParams: JSON.stringify(validatedParams, null, 2),
              outputResult: JSON.stringify(stepResult, null, 2),
              isSuccess: stepResult?.errCode === null || stepResult?.errCode === 0,
              errorCode: stepResult?.errCode,
              errorMessage: stepResult?.errMsg,
              createdTaskId: stepResult?.data?.id,
              createdTaskTitle: stepResult?.data?.title,
              callDuration: toolCallEndTime - toolCallStartTime,
              timestamp: new Date().toISOString(),
            })
          }

          // 记录工具调用性能
          performanceMonitor.recordToolCall(step.toolName, true, toolCallEndTime - toolCallStartTime, step.retryCount)

          // 存储结果到上下文
          logger.debug('存储结果到上下文', {
            stepId: step.stepId,
            contextId: context.sessionId,
            resultKeys: stepResult ? Object.keys(stepResult) : [],
          })

          context.setStepResult(step.stepId, stepResult)

          step.status = 'completed'
          step.executionTime = Date.now() - stepStartTime
          stepSuccess = true

          logger.info('步骤执行成功', {
            stepId: step.stepId,
            toolName: step.toolName,
            status: step.status,
            executionTime: step.executionTime,
            totalAttempts: step.retryCount + 1,
            success: true,
          })

          // 推送步骤执行结果
          await sseChannel.write({
            type: 'step_result',
            stepId: step.stepId,
            result: stepResult,
            executionTime: step.executionTime,
            contextUpdates: getContextUpdates(context),
            retryCount: step.retryCount,
            timestamp: Date.now(),
          })

          logger.debug('步骤结果推送完成', {
            stepId: step.stepId,
            pushSuccess: true,
          })

          break // 成功执行，跳出重试循环
        } catch (error) {
          const errorTime = Date.now()
          step.executionTime = errorTime - stepStartTime

          logger.error('步骤执行异常', error, {
            stepId: step.stepId,
            toolName: step.toolName,
            method: step.method || 'unknown',
            attemptNumber: step.retryCount + 1,
            maxAttempts: step.maxRetries + 1,
            errorName: error.name,
            errorMessage: error.message,
            errorStack: error.stack,
            executionTime: step.executionTime,
            errorTime: new Date(errorTime).toISOString(),
          })

          // 特别关注任务创建相关的错误
          if (step.toolName === 'createTask' || (step.method && step.method.includes('createTask'))) {
            logger.error('!!! 任务创建工具调用失败 !!!', error, {
              stepId: step.stepId,
              toolName: step.toolName,
              method: step.method,
              attemptNumber: step.retryCount + 1,
              errorDetails: {
                name: error.name,
                message: error.message,
                stack: error.stack,
              },
              inputParams: step.parameters ? JSON.stringify(step.parameters, null, 2) : 'no params',
              timestamp: new Date().toISOString(),
            })
          }

          // 记录错误和性能
          performanceMonitor.recordError('step_execution', error.message, {
            stepId: step.stepId,
            toolName: step.toolName,
            retryCount: step.retryCount,
          })

          logger.debug('调用增强错误处理器', {
            stepId: step.stepId,
            toolName: step.toolName,
            errorHandlerType: 'EnhancedErrorHandler',
          })

          // 增强的错误处理
          const errorHandleResult = await EnhancedErrorHandler.handleToolError(error, step, context, sseChannel)

          logger.info('错误处理器返回结果', {
            stepId: step.stepId,
            toolName: step.toolName,
            errorHandleResult:
              typeof errorHandleResult === 'object' ? JSON.stringify(errorHandleResult, null, 2) : errorHandleResult,
            resultType: typeof errorHandleResult,
          })

          if (errorHandleResult === 'retry') {
            logger.info('错误处理器建议重试', {
              stepId: step.stepId,
              toolName: step.toolName,
              currentAttempt: step.retryCount + 1,
              maxAttempts: step.maxRetries + 1,
              willRetry: step.retryCount < step.maxRetries,
            })
            // 继续重试循环
            continue
          } else if (errorHandleResult && typeof errorHandleResult === 'object') {
            // 不再支持降级处理，直接失败
            logger.error('错误处理器返回了降级结果，但系统不再支持降级处理', {
              stepId: step.stepId,
              toolName: step.toolName,
              errorHandleResult: JSON.stringify(errorHandleResult, null, 2),
            })
            step.status = 'failed'
            step.error = error.message
            throw error
          } else {
            logger.error('错误处理器无法处理，步骤最终失败', {
              stepId: step.stepId,
              toolName: step.toolName,
              attemptNumber: step.retryCount + 1,
              maxAttempts: step.maxRetries + 1,
              finalError: error.message,
            })
            // 最终失败
            step.status = 'failed'
            step.error = error.message
            throw error
          }
        }
      }

      if (!stepSuccess) {
        throw new Error(`步骤 ${step.stepId} 执行失败，已达到最大重试次数`)
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 记录计划执行性能
    performanceMonitor.recordPlanGeneration(planStartTime, executionPlan.endTime)

    // 检查是否为查询类任务，需要特殊处理结果
    const isQueryTask = await checkIfQueryTask(executionPlan, context)

    if (isQueryTask) {
      logger.info('检测到查询类任务，处理查询结果', {
        planId: executionPlan.planId,
        taskType: 'query',
      })

      // 获取查询结果并格式化
      const queryResult = await processQueryTaskResult(executionPlan, context, sseChannel)

      // 推送格式化的查询结果
      await sseChannel.write({
        type: 'query_result_formatted',
        plan: executionPlan,
        queryResult: queryResult,
        summary: generateExecutionSummary(executionPlan, context),
        timestamp: Date.now(),
      })
    } else {
      // 操作类任务，推送标准的执行完成消息
      await sseChannel.write({
        type: 'execution_complete',
        plan: executionPlan,
        summary: generateExecutionSummary(executionPlan, context),
        performanceReport: performanceMonitor.getPerformanceReport(),
        timestamp: Date.now(),
      })
    }

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - planStartTime

    // 记录失败的计划执行
    performanceMonitor.recordError('plan_execution', error.message, {
      planId: executionPlan.planId,
      totalSteps: executionPlan.totalSteps,
    })

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      performanceReport: performanceMonitor.getPerformanceReport(),
      timestamp: Date.now(),
    })

    throw error
  }
}

/**
 * 检查是否为查询类任务
 * @param {Object} executionPlan - 执行计划
 * @param {Object} context - 执行上下文
 * @returns {boolean} 是否为查询类任务
 */
async function checkIfQueryTask(executionPlan, context) {
  const logger = createLogger('executor')

  // 检查计划中是否包含查询类工具
  const queryTools = ['getTasks', 'getProjects', 'getProject', 'getTask']
  const hasQueryTool = executionPlan.steps.some((step) => queryTools.includes(step.toolName))

  // 检查AI分析结果中的任务类型
  const taskType = executionPlan.taskType || 'operation'
  const isQueryType = taskType === 'query'

  // 检查是否有processQueryResult步骤
  const hasProcessStep = executionPlan.steps.some((step) => step.toolName === 'processQueryResult')

  const isQuery = hasQueryTool || isQueryType || hasProcessStep

  logger.info('查询任务检测结果', {
    planId: executionPlan.planId,
    hasQueryTool,
    taskType,
    isQueryType,
    hasProcessStep,
    finalResult: isQuery,
    queryToolsFound: executionPlan.steps.filter((step) => queryTools.includes(step.toolName)).map((s) => s.toolName),
  })

  return isQuery
}

/**
 * 处理查询类任务的结果
 * @param {Object} executionPlan - 执行计划
 * @param {Object} context - 执行上下文
 * @param {Object} sseChannel - SSE通道
 * @returns {Object} 格式化的查询结果
 */
async function processQueryTaskResult(executionPlan, context, sseChannel) {
  const logger = createLogger('executor')
  const processStartTime = Date.now()

  logger.info('开始处理查询任务结果', {
    planId: executionPlan.planId,
    totalSteps: executionPlan.steps.length,
    startTime: new Date(processStartTime).toISOString(),
  })

  try {
    // 查找查询类工具的执行结果
    const queryResults = []
    const queryTools = ['getTasks', 'getProjects', 'getProject', 'getTask']

    for (const step of executionPlan.steps) {
      if (queryTools.includes(step.toolName) && step.status === 'completed') {
        const stepResult = context.getStepResult(step.stepId)
        if (stepResult && stepResult.data) {
          queryResults.push({
            toolName: step.toolName,
            stepId: step.stepId,
            data: stepResult.data,
            description: step.description,
          })
        }
      }
    }

    logger.info('收集到的查询结果', {
      planId: executionPlan.planId,
      resultCount: queryResults.length,
      results: queryResults.map((r) => ({
        toolName: r.toolName,
        stepId: r.stepId,
        dataType: Array.isArray(r.data) ? 'array' : typeof r.data,
        dataLength: Array.isArray(r.data) ? r.data.length : 'N/A',
      })),
    })

    // 生成用户友好的回复
    let formattedResponse = ''
    let summary = ''
    let totalItems = 0

    if (queryResults.length === 0) {
      formattedResponse = '没有找到相关数据。'
      summary = '查询完成，无结果'
    } else {
      // 处理不同类型的查询结果
      for (const result of queryResults) {
        switch (result.toolName) {
          case 'getTasks':
            const tasks = result.data || []
            totalItems += tasks.length
            if (tasks.length === 0) {
              formattedResponse += '没有找到符合条件的任务。\n'
            } else {
              formattedResponse += `找到 ${tasks.length} 个任务：\n\n`
              tasks.slice(0, 10).forEach((task, index) => {
                const status = task.status === 0 ? '未完成' : '已完成'
                const priority = ['最低', '低', '', '中', '', '高'][task.priority] || '普通'
                formattedResponse += `${index + 1}. ${task.title}\n`
                formattedResponse += `   状态：${status} | 优先级：${priority}\n`
                if (task.projectName) {
                  formattedResponse += `   项目：${task.projectName}\n`
                }
                if (task.dueDate) {
                  formattedResponse += `   截止时间：${task.dueDate}\n`
                }
                formattedResponse += '\n'
              })
              if (tasks.length > 10) {
                formattedResponse += `... 还有 ${tasks.length - 10} 个任务\n`
              }
            }
            break

          case 'getProjects':
            const projects = result.data || []
            totalItems += projects.length
            if (projects.length === 0) {
              formattedResponse += '没有找到符合条件的项目。\n'
            } else {
              formattedResponse += `找到 ${projects.length} 个项目：\n\n`
              projects.forEach((project, index) => {
                formattedResponse += `${index + 1}. ${project.name}\n`
                if (project.kind) {
                  formattedResponse += `   类型：${project.kind === 'TASK' ? '任务项目' : '笔记项目'}\n`
                }
                formattedResponse += '\n'
              })
            }
            break

          default:
            // 其他查询类型的通用处理
            if (Array.isArray(result.data)) {
              totalItems += result.data.length
              formattedResponse += `查询结果：找到 ${result.data.length} 条记录\n`
            } else {
              formattedResponse += `查询结果：${JSON.stringify(result.data, null, 2)}\n`
            }
        }
      }

      summary = `查询完成，共找到 ${totalItems} 条记录`
    }

    const processedResult = {
      formattedResponse: formattedResponse.trim(),
      summary,
      totalItems,
      queryResults,
      hasMore: totalItems > 10,
    }

    const processEndTime = Date.now()
    const processDuration = processEndTime - processStartTime

    logger.info('查询结果处理完成', {
      planId: executionPlan.planId,
      totalItems,
      responseLength: formattedResponse.length,
      summary,
      processDuration,
      endTime: new Date(processEndTime).toISOString(),
    })

    return processedResult
  } catch (error) {
    const processEndTime = Date.now()
    const processDuration = processEndTime - processStartTime

    logger.error('查询结果处理失败', error, {
      planId: executionPlan.planId,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      processDuration,
      endTime: new Date(processEndTime).toISOString(),
    })

    // 返回错误结果而不是抛出异常
    return {
      formattedResponse: '抱歉，处理查询结果时出现错误，请稍后重试。',
      summary: '查询结果处理失败',
      totalItems: 0,
      queryResults: [],
      hasMore: false,
      error: error.message,
    }
  }
}

module.exports = {
  executeRobustPlan,
}
